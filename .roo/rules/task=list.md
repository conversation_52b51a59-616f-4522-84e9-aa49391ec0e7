---
description: 
globs: 
alwaysApply: false
---
# 🧠 Knowledge Base Feature Implementation

_As you complete tasks and reference relevant files, update this file as our memory to help with future tasks._

This file is structured to track the development of the knowledge base system and integrations with large language models. Please keep this file updated as Roo Code progresses through the implementation flow. ✅




## 🧠 Roo Code Rules

When interacting with this file:

> Always update completed tasks with [x] when done.
> Prompt user approval before starting the next phase.
> Log any relevant files changed next to the task (in a comment or sub-bullet).
> Do not auto-complete future tasks until explicitly requested.
> Tasks are grouped by development phase. Follow the order.
> Use consistent naming and structure when adding new tasks to improve readability and navigation.
> Link to related documentation or resources when applicable for easier context and traceability.
> If a task is blocked, clearly note the reason and any dependencies or decisions needed to proceed.
> Review completed phases for completeness before marking the next phase ready.
> When collaborating, clearly assign responsibility for tasks (e.g., initials or team name).
> Add timestamps (optional) for when tasks are completed or updated, to maintain a timeline.
> Use inline comments for minor notes or rationale if the implementation deviates from the original plan.
> Keep this file lean—archive or summarize older tasks when a phase is fully complete and no longer actively referenced.
