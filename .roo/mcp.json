{"mcpServers": {"Bright Data": {"command": "npx", "args": ["@brightdata/mcp"], "env": {"API_TOKEN": "fd358d1920658af81dcd8b442154950efd141500fe564d360a1d25b6c818234c", "WEB_UNLOCKER_ZONE": "web_unlocker1", "BROWSER_AUTH": "wss://brd-customer-hl_e6b08106-zone-scraping_browser2:<EMAIL>:9222"}, "disabled": false, "alwaysAllow": []}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAJuPah0gzC7mCh62saJiUxINoWn90"}, "disabled": false, "alwaysAllow": []}, "cloudflare-bindings": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-cloudflare-bindings"], "env": {"CLOUDFLARE_ACCOUNT_ID": "your-account-id", "CLOUDFLARE_API_TOKEN": "your-api-token"}, "disabled": false, "alwaysAllow": []}, "cloudflare-observability": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-cloudflare-observability"], "env": {"CLOUDFLARE_ACCOUNT_ID": "your-account-id", "CLOUDFLARE_API_TOKEN": "your-api-token"}, "disabled": false, "alwaysAllow": []}}}