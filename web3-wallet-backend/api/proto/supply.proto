syntax = "proto3";

package supply;

option go_package = "github.com/Dima<PERSON><PERSON>ti/go-coffee/web3-wallet-backend/api/proto/supply";

import "google/protobuf/timestamp.proto";

// Supply service for managing coffee supply chain
service SupplyService {
  // Create a new supply entry
  rpc CreateSupply(CreateSupplyRequest) returns (CreateSupplyResponse);
  
  // Get supply by ID
  rpc GetSupply(GetSupplyRequest) returns (GetSupplyResponse);
  
  // List supplies with pagination
  rpc ListSupplies(ListSuppliesRequest) returns (ListSuppliesResponse);
  
  // Update supply status
  rpc UpdateSupplyStatus(UpdateSupplyStatusRequest) returns (UpdateSupplyStatusResponse);
  
  // Track supply location
  rpc TrackSupply(TrackSupplyRequest) returns (TrackSupplyResponse);
}

// Supply represents a coffee supply entry
message Supply {
  string id = 1;
  string origin = 2;
  string variety = 3;
  double quantity = 4;
  string unit = 5;
  SupplyStatus status = 6;
  string location = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  repeated SupplyEvent events = 10;
}

// Supply status enumeration
enum SupplyStatus {
  SUPPLY_STATUS_UNKNOWN = 0;
  SUPPLY_STATUS_HARVESTED = 1;
  SUPPLY_STATUS_PROCESSED = 2;
  SUPPLY_STATUS_SHIPPED = 3;
  SUPPLY_STATUS_DELIVERED = 4;
  SUPPLY_STATUS_SOLD = 5;
}

// Supply event for tracking
message SupplyEvent {
  string id = 1;
  string supply_id = 2;
  string event_type = 3;
  string description = 4;
  string location = 5;
  google.protobuf.Timestamp timestamp = 6;
  map<string, string> metadata = 7;
}

// Request messages
message CreateSupplyRequest {
  string origin = 1;
  string variety = 2;
  double quantity = 3;
  string unit = 4;
  string location = 5;
}

message CreateSupplyResponse {
  Supply supply = 1;
}

message GetSupplyRequest {
  string id = 1;
}

message GetSupplyResponse {
  Supply supply = 1;
}

message ListSuppliesRequest {
  int32 page = 1;
  int32 page_size = 2;
  string filter = 3;
  string sort_by = 4;
}

message ListSuppliesResponse {
  repeated Supply supplies = 1;
  int32 total_count = 2;
  int32 page = 3;
  int32 page_size = 4;
}

message UpdateSupplyStatusRequest {
  string id = 1;
  SupplyStatus status = 2;
  string location = 3;
  string notes = 4;
}

message UpdateSupplyStatusResponse {
  Supply supply = 1;
}

message TrackSupplyRequest {
  string id = 1;
}

message TrackSupplyResponse {
  Supply supply = 1;
  repeated SupplyEvent tracking_history = 2;
}
