// Code generated by protoc-gen-go. DO NOT EDIT.
package wallet

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

// WalletRequest represents a wallet operation request
type WalletRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Address   string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	Operation string `protobuf:"bytes,3,opt,name=operation,proto3" json:"operation,omitempty"`
	Amount    string `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *WalletRequest) Reset() {
	*x = WalletRequest{}
}

func (x *WalletRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WalletRequest) ProtoMessage() {}

func (x *WalletRequest) ProtoReflect() protoreflect.Message {
	return nil
}

func (x *WalletRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *WalletRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *WalletRequest) GetOperation() string {
	if x != nil {
		return x.Operation
	}
	return ""
}

func (x *WalletRequest) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

// WalletResponse represents a wallet operation response
type WalletResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success         bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message         string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	TransactionHash string                 `protobuf:"bytes,3,opt,name=transaction_hash,json=transactionHash,proto3" json:"transaction_hash,omitempty"`
	Balance         string                 `protobuf:"bytes,4,opt,name=balance,proto3" json:"balance,omitempty"`
	Timestamp       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *WalletResponse) Reset() {
	*x = WalletResponse{}
}

func (x *WalletResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WalletResponse) ProtoMessage() {}

func (x *WalletResponse) ProtoReflect() protoreflect.Message {
	return nil
}

func (x *WalletResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *WalletResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *WalletResponse) GetTransactionHash() string {
	if x != nil {
		return x.TransactionHash
	}
	return ""
}

func (x *WalletResponse) GetBalance() string {
	if x != nil {
		return x.Balance
	}
	return ""
}

func (x *WalletResponse) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

// CreateWalletRequest represents a create wallet request
type CreateWalletRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	Network  string `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
}

func (x *CreateWalletRequest) Reset() {
	*x = CreateWalletRequest{}
}

func (x *CreateWalletRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWalletRequest) ProtoMessage() {}

func (x *CreateWalletRequest) ProtoReflect() protoreflect.Message {
	return nil
}

// CreateWalletResponse represents a create wallet response
type CreateWalletResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address    string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	PrivateKey string `protobuf:"bytes,2,opt,name=private_key,json=privateKey,proto3" json:"private_key,omitempty"`
	Mnemonic   string `protobuf:"bytes,3,opt,name=mnemonic,proto3" json:"mnemonic,omitempty"`
	Success    bool   `protobuf:"varint,4,opt,name=success,proto3" json:"success,omitempty"`
	Message    string `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *CreateWalletResponse) Reset() {
	*x = CreateWalletResponse{}
}

func (x *CreateWalletResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWalletResponse) ProtoMessage() {}

func (x *CreateWalletResponse) ProtoReflect() protoreflect.Message {
	return nil
}

// WalletServiceClient is the client API for WalletService service.
type WalletServiceClient interface {
	CreateWallet(ctx context.Context, in *CreateWalletRequest, opts ...grpc.CallOption) (*CreateWalletResponse, error)
	GetBalance(ctx context.Context, in *WalletRequest, opts ...grpc.CallOption) (*WalletResponse, error)
	SendTransaction(ctx context.Context, in *WalletRequest, opts ...grpc.CallOption) (*WalletResponse, error)
}

type walletServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWalletServiceClient(cc grpc.ClientConnInterface) WalletServiceClient {
	return &walletServiceClient{cc}
}

func (c *walletServiceClient) CreateWallet(ctx context.Context, in *CreateWalletRequest, opts ...grpc.CallOption) (*CreateWalletResponse, error) {
	out := new(CreateWalletResponse)
	err := c.cc.Invoke(ctx, "/wallet.WalletService/CreateWallet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetBalance(ctx context.Context, in *WalletRequest, opts ...grpc.CallOption) (*WalletResponse, error) {
	out := new(WalletResponse)
	err := c.cc.Invoke(ctx, "/wallet.WalletService/GetBalance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) SendTransaction(ctx context.Context, in *WalletRequest, opts ...grpc.CallOption) (*WalletResponse, error) {
	out := new(WalletResponse)
	err := c.cc.Invoke(ctx, "/wallet.WalletService/SendTransaction", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WalletServiceServer is the server API for WalletService service.
type WalletServiceServer interface {
	CreateWallet(context.Context, *CreateWalletRequest) (*CreateWalletResponse, error)
	GetBalance(context.Context, *WalletRequest) (*WalletResponse, error)
	SendTransaction(context.Context, *WalletRequest) (*WalletResponse, error)
}

// UnimplementedWalletServiceServer can be embedded to have forward compatible implementations.
type UnimplementedWalletServiceServer struct {
}

func (*UnimplementedWalletServiceServer) CreateWallet(context.Context, *CreateWalletRequest) (*CreateWalletResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWallet not implemented")
}

func (*UnimplementedWalletServiceServer) GetBalance(context.Context, *WalletRequest) (*WalletResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBalance not implemented")
}

func (*UnimplementedWalletServiceServer) SendTransaction(context.Context, *WalletRequest) (*WalletResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendTransaction not implemented")
}

func RegisterWalletServiceServer(s *grpc.Server, srv WalletServiceServer) {
	s.RegisterService(&_WalletService_serviceDesc, srv)
}

var _WalletService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "wallet.WalletService",
	HandlerType: (*WalletServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateWallet",
			Handler:    _WalletService_CreateWallet_Handler,
		},
		{
			MethodName: "GetBalance",
			Handler:    _WalletService_GetBalance_Handler,
		},
		{
			MethodName: "SendTransaction",
			Handler:    _WalletService_SendTransaction_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "wallet.proto",
}

func _WalletService_CreateWallet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWalletRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).CreateWallet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/wallet.WalletService/CreateWallet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).CreateWallet(ctx, req.(*CreateWalletRequest))
	}
	return interceptor(ctx, info, handler)
}

func _WalletService_GetBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WalletRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/wallet.WalletService/GetBalance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetBalance(ctx, req.(*WalletRequest))
	}
	return interceptor(ctx, info, handler)
}

func _WalletService_SendTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WalletRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).SendTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/wallet.WalletService/SendTransaction",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).SendTransaction(ctx, req.(*WalletRequest))
	}
	return interceptor(ctx, info, handler)
}
