apiVersion: apps/v1
kind: Deployment
metadata:
  name: fintech-api
  namespace: fintech-platform
  labels:
    app: fintech-api
    component: api
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: fintech-api
  template:
    metadata:
      labels:
        app: fintech-api
        component: api
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      imagePullSecrets:
      - name: docker-registry-secret
      containers:
      - name: fintech-api
        image: ghcr.io/dimajoyti/go-coffee/fintech-platform:latest
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        - containerPort: 9090
          name: metrics
          protocol: TCP
        env:
        - name: CONFIG_PATH
          value: "/app/config/config.yaml"
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: fintech-secrets
              key: DATABASE_PASSWORD
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: fintech-secrets
              key: JWT_SECRET
        - name: WEBHOOK_SECRET
          valueFrom:
            secretKeyRef:
              name: fintech-secrets
              key: WEBHOOK_SECRET
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: fintech-secrets
              key: ENCRYPTION_KEY
        - name: STRIPE_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: fintech-secrets
              key: STRIPE_SECRET_KEY
        - name: CIRCLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: fintech-secrets
              key: CIRCLE_API_KEY
        - name: ETHEREUM_PRIVATE_KEY
          valueFrom:
            secretKeyRef:
              name: fintech-secrets
              key: ETHEREUM_PRIVATE_KEY
        - name: SOLANA_PRIVATE_KEY
          valueFrom:
            secretKeyRef:
              name: fintech-secrets
              key: SOLANA_PRIVATE_KEY
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: temp-storage
          mountPath: /tmp
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 30
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: config-volume
        configMap:
          name: fintech-config
      - name: temp-storage
        emptyDir: {}
      securityContext:
        fsGroup: 1000
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - fintech-api
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: fintech-api-service
  namespace: fintech-platform
  labels:
    app: fintech-api
    component: api
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: metrics
  selector:
    app: fintech-api

---
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: fintech-api-hpa
  namespace: fintech-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: fintech-api
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max

---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: fintech-api-pdb
  namespace: fintech-platform
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: fintech-api

---
# Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: fintech-api-network-policy
  namespace: fintech-platform
spec:
  podSelector:
    matchLabels:
      app: fintech-api
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: fintech-platform
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 9090
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: fintech-platform
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
  - to: []  # Allow external traffic (APIs, webhooks, etc.)
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  - to: []  # DNS
    ports:
    - protocol: UDP
      port: 53

---
# Service Monitor for Prometheus
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: fintech-api-metrics
  namespace: fintech-platform
  labels:
    app: fintech-api
    component: api
spec:
  selector:
    matchLabels:
      app: fintech-api
  endpoints:
  - port: metrics
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s

---
# Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fintech-api-ingress
  namespace: fintech-platform
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "30"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.fintech-platform.com
    secretName: fintech-api-tls
  rules:
  - host: api.fintech-platform.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fintech-api-service
            port:
              number: 80
